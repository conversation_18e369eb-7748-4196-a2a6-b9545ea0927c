from unittest.mock import AsyncMock, <PERSON><PERSON>ock

import pytest
from google.adk.agents import LlmAgent
from google.adk.events import Event, EventActions

from cyber_ai_agent.attacker.attacker import AttackerPromptInjectionAgent
from cyber_ai_agent.models.schemas import LogAttack, Objective


@pytest.fixture
def target_dummy():
    llm_victim = AsyncMock(spec=LlmAgent)

    async def mock_run_async(ctx):
        event_mock = MagicMock()
        event_mock.is_final_response = True
        event_mock.content = "response_dummy"
        print("returnging", event_mock)

        yield event_mock

    llm_victim.run_async = mock_run_async

    return llm_victim


class AttackerDummy(AttackerPromptInjectionAgent):
    def __init__(self, llm_target):
        super().__init__(name="dummy", llm_target=llm_target)

    async def _run_async_impl(self, ctx):
        objective: Objective = ctx.session.state.get("objective", None)
        ctx.session.state.setdefault("attack_logs", [])
        ctx.session.state.setdefault("llm_calls", 0)
        attack_prompt = "coucou"
        ctx.session.state["query"] = attack_prompt

        final_response = ""
        async for event in self.llm_target.run_async(ctx):
            if event.is_final_response:
                final_response = str(event.content)

        ctx.session.state["llm_calls"] += 1
        ctx.session.state["attack_logs"].append(
            LogAttack(
                iteration=ctx.session.state["llm_calls"],
                prompt=attack_prompt,
                response=final_response,
                attacker_kind="dummy",
                objective=objective,
            )
        )
        yield Event(
            content=None,
            author=self.name,
            actions=EventActions(
                state_delta={
                    "attack_logs": ctx.session.state.get("attack_logs", []),
                }
            ),
        )


@pytest.fixture
def attacker_dummy(target_dummy):
    attacker = AttackerDummy(llm_target=target_dummy)
    return attacker


@pytest.fixture
def objective_dummy():
    objective = Objective(name="obj_dummy", prompt_reference="prompt_ref_dummy")
    return objective


@pytest.fixture
def settings_dummy(objective_dummy):
    settings = MagicMock()
    settings.objectives = [objective_dummy]
    sess_cfg = MagicMock()
    sess_cfg.app_name = "app_name_dummy"
    sess_cfg.user_id = "user_id_dummy"
    sess_cfg.session_id = "session_id_dummy"
    settings.session.model_copy.return_value = sess_cfg
    return settings
