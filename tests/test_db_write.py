# test_db_write.py

import os
from datetime import datetime

from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, OperationFailure

# Configuration MongoDB mise à jour
# Utilise les nouvelles credentials configurées dans .env
MONGO_URI = os.getenv(
    "MONGO_URI",
    "**********************************************************************************",
)


def main():
    try:
        # Établir la connexion
        client = MongoClient(MONGO_URI)

        # Tester la connexion en envoyant une commande simple (ping)
        client.admin.command("ping")
        print("Connecté à MongoDB avec succès !")

        # Accéder à la base de données configurée
        db = client.cyber_ai_agent_db  # Base de données principale
        collection = db.prompt_responses  # Collection principale

        # Document à insérer (format compatible avec la collection prompt_responses)
        test_document = {
            "prompte": "Test depuis le script Python local",
            "heure": datetime.now(),
            "reponse": "Ceci est un test de connexion MongoDB réussi !",
            "source": "python_test_script",
        }

        # Insérer le document
        insert_result = collection.insert_one(test_document)
        print(f"Document inséré avec l'ID : {insert_result.inserted_id}")

        # Vérifier l'insertion en retrouvant le document
        found_document = collection.find_one({"_id": insert_result.inserted_id})
        print(f"Document trouvé : {found_document}")

        # Optionnel : Nettoyer (supprimer le document de test)
        # collection.delete_one({"_id": insert_result.inserted_id})
        # print("Document de test nettoyé.")

    except ConnectionFailure as e:
        print(f"Échec de la connexion à MongoDB : {e}")
        print(
            "Vérifiez que votre conteneur MongoDB est en cours d'exécution et accessible sur localhost:27017."
        )
        print("Vérifiez également votre variable d'environnement MONGO_URI ou l'URI hardcodée.")
    except OperationFailure as e:
        print(f"Échec de l'opération MongoDB (problème d'authentification/autorisation ?) : {e}")
        print(
            "Vérifiez le nom d'utilisateur et le mot de passe dans votre MONGO_URI et docker-compose.yml."
        )
    except Exception as e:
        print(f"Une erreur inattendue est survenue : {e}")
    finally:
        # Assurez-vous de fermer la connexion
        if "client" in locals() and client:
            client.close()
            print("Connexion MongoDB fermée.")


if __name__ == "__main__":
    main()
