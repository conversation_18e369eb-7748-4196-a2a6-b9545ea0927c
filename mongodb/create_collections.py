# mongodb/create_collections.py
"""
Ce script est destiné à être intégré au démarrage de votre service,
à exécuter une seule fois pour initialiser la collection avec un schéma de validation.
"""

from pymongo.errors import CollectionInvalid

from cyber_ai_agent.mongo_utils import get_database


def create_prompt_responses_collection_with_validation(
    db_name: str = "cyber_ai_agent_db", collection_name: str = "prompt_responses"
) -> None:
    """
    Crée la collection 'prompt_responses' avec un schéma de validation.
    Si la collection existe déjà, un message l'indique.
    """
    try:
        db = get_database(db_name)

        # Définition du schéma de validation JSON pour la collection 'prompt_responses'
        # Cela garantit que chaque document inséré contient les champs 'prompte', 'heure', 'reponse'
        # avec les types BSON spécifiés.
        prompt_response_validator = {
            "$jsonSchema": {
                "bsonType": "object",
                "required": ["prompte", "heure", "reponse"],
                "properties": {
                    "prompte": {
                        "bsonType": "string",
                        "description": "Le texte de la question posée.",
                    },
                    "heure": {
                        "bsonType": "date",
                        "description": "La date et l'heure de la requête.",
                    },
                    "reponse": {"bsonType": "string", "description": "La réponse générée."},
                    # _id est automatiquement ajouté par MongoDB et est de type objectId
                    "_id": {
                        "bsonType": "objectId",
                        "description": "L'identifiant unique du document.",
                    },
                },
            }
        }

        # Tente de créer la collection avec le validateur
        db.create_collection(collection_name, validator=prompt_response_validator)
        print(f"Collection '{collection_name}' créée avec validation de schéma.")
    except CollectionInvalid:
        # Cette erreur est levée si la collection existe déjà
        print(
            f"Collection '{collection_name}' existe déjà. Validation non appliquée (ou mise à jour nécessaire)."
        )
    except Exception as e:
        # Capture toute autre erreur inattendue
        print(f"Erreur lors de la création de la collection avec validation : {e}")


if __name__ == "__main__":
    # Ce bloc s'exécute lorsque le script est lancé directement
    create_prompt_responses_collection_with_validation()
