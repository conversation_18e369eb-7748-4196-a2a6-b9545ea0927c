# mongodb/test_db_write.py
"""
Ce script teste l'écriture de données dans la collection 'prompt_responses' de MongoDB.
Il insère un document de test et le vérifie.
"""

from datetime import datetime

from cyber_ai_agent_adk.config import close_mongo_client, get_database
from pymongo.errors import ConnectionFailure, OperationFailure

if __name__ == "__main__":
    # Informations du document à insérer pour le test
    prompt_text_to_insert = "Comment tester l'écriture dans MongoDB ?"
    response_text_to_insert = "En insérant un document de test avec un script Python."

    try:
        # Obtient l'instance de la base de données via la fonction get_database
        db = get_database()
        # Accède à la collection 'prompt_responses'
        collection = db["prompt_responses"]

        # Crée le document à insérer, en respectant le schéma défini dans create_collection.py
        document_to_insert = {
            "prompte": prompt_text_to_insert,
            "heure": datetime.now(),  # Utilise l'heure actuelle
            "reponse": response_text_to_insert,
        }

        # Insère le document dans la collection
        result = collection.insert_one(document_to_insert)
        print(f"Document inséré avec l'ID : {result.inserted_id}")

        # Vérification de l'insertion en recherchant le document par son ID
        found_doc = collection.find_one({"_id": result.inserted_id})
        print(f"Document trouvé après insertion : {found_doc}")

    except ConnectionFailure as e:
        # Gère les erreurs de connexion à MongoDB
        print(f"La connexion à MongoDB a échoué : {e}")
    except OperationFailure as e:
        # Gère les erreurs d'opération MongoDB (par exemple, violation de schéma)
        print(f"L'opération MongoDB a échoué : {e}")
        print(f"Détails de l'erreur : {e.details if hasattr(e, 'details') else 'N/A'}")
    except Exception as e:
        # Capture toute autre erreur inattendue
        print(f"Une erreur inattendue est survenue : {e}")
    finally:
        # Assurez-vous de fermer la connexion MongoDB à la fin du script
        # Cela est important si le client n'est pas géré comme un singleton pour une application plus longue
        close_mongo_client()
        print("Connexion MongoDB fermée (si ouverte).")
