# MongoDB Setup Guide

This guide explains how to set up and use MongoDB with <PERSON><PERSON> in the Cyber AI Agent project.

## 🚀 Quick Start Options

### Option 1: MongoDB Only (Command Line)
For basic MongoDB setup without web interface:

```bash
# Start MongoDB
make mongodb-start
```

# Setup collections and test connection
# NB : the collection should already have been created in the previous step by mongo-init.js script
```bash
make mongodb-setup
```

### Option 2: MongoDB + Web Interface (Recommended)
For MongoDB with visual web interface:

```bash
# Start MongoDB with web interface (all-in-one)
make mongodb-ui
```
**Then access:** http://localhost:8081

### Option 3: All Services
For complete application stack:

```bash
# Start all services (MongoDB + Application + Web Interface)
make start-all
```

## 🛑 Stop Services

```bash
# Stop all services
make mongodb-stop
```

## Configuration

### Environment Variables

The MongoDB configuration is managed through the `.env` file:

```env
# MongoDB Configuration
MONGO_ROOT_USERNAME=cyber_admin
MONGO_ROOT_PASSWORD=cyber_secure_password_2024
MONGODB_HOST=localhost
MONGODB_PORT=27017
MONGODB_APP_DB_NAME=cyber_ai_agent_db
```

### Docker Compose

The `Docker-compose.yaml` file defines:
- MongoDB service with persistent storage
- Automatic database initialization
- Health checks
- Network configuration between services
- **Mongo Express** web interface for database management

## 🖥️ Web Interface - Mongo Express

### Accessing the MongoDB Interface

Once your containers are running, you can access the MongoDB web interface:

**URL:** http://localhost:8081

### Features Available

- **Database Navigation**: Browse all your databases
- **Collection Management**: Create, modify, delete collections
- **Document Visualization**: View and edit JSON documents
- **Query Execution**: Run MongoDB queries directly
- **Import/Export**: Import or export your data
- **Performance Statistics**: View database metrics

### Navigation Guide

1. **Homepage**: Lists all available databases
2. **Database View**: Click on `cyber_ai_agent_db` to see your collections
3. **Collections**:
   - `prompt_responses`: Logs of AI model interactions
   - `attack_results`: Attack execution results
   - `evaluation_results`: Evaluation outcomes

## Database Schema

### Collections

#### `prompt_responses`
Stores AI prompt-response pairs with validation:

```javascript
{
  "prompte": "string",     // The input prompt
  "heure": "date",         // Timestamp
  "reponse": "string",     // AI response
  "_id": "objectId"        // Unique identifier
}
```

## Usage Examples

### Basic Connection

```python
from cyber_ai_agent.config import get_database, close_mongo_client
from datetime import datetime

# Get database instance
db = get_database()

# Get collection
collection = db['prompt_responses']

# Insert document
result = collection.insert_one({
    "prompte": "What is cybersecurity?",
    "heure": datetime.now(),
    "reponse": "Cybersecurity is the practice of protecting systems..."
})

# Close connection when done
close_mongo_client()
```

### Running Tests

```bash
# Test database connection and operations
python tests/test_db_write.py

# Create collections with validation
python -m mongodb.create_collections

# Start MongoDB with web interface
make mongodb-ui
```

## Troubleshooting

### Common Issues

1. **Connection Failed**:
   - Ensure Docker containers are running: `docker compose ps`
   - Check environment variables in `.env`
   - Verify network connectivity: `docker network ls`

2. **Authentication Error**:
   - Verify credentials in `.env` match `Docker-compose.yaml`
   - Check MongoDB logs: `docker compose logs mongodb`

3. **Import Errors**:
   - Ensure you're running scripts from the project root
   - Check Python path configuration

### Useful Commands

```bash
# View MongoDB logs
docker compose logs mongodb

# Connect to MongoDB shell
docker exec -it cyber-ai-mongodb mongosh -u cyber_admin -p cyber_secure_password_2024

# Reset MongoDB data (WARNING: Deletes all data)
docker compose down -v
docker volume rm cyber_ai_agent_mongodb_data
```

## Security Notes

- Change default passwords in production
- Use environment variables for sensitive data
- Consider using MongoDB Atlas for production deployments
- Implement proper backup strategies

## Performance Tips

- Indexes are automatically created for common queries
- Monitor query performance with MongoDB Compass
- Consider sharding for large datasets
- Use connection pooling in production
