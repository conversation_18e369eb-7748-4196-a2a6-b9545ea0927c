# cyber_ai_agent

!!! note "What is cyber_ai_agent?"
    Development of AI agents for detecting cybersecurity issues on deployed agents.

## Example

The recommended way to run the code of this project is via CLI:

```bash
cyber-ai-agent --help
```

which should return the list of the available commands:

```text
 Usage: cyber-ai-agent [OPTIONS] COMMAND [ARGS]...

 Run all datascience commands.

╭─ Commands ──────────────────────────────────────────────────╮
│ data     Manages data flow.                                 │
│ model    Manages model training and predictions.            │
╰─────────────────────────────────────────────────────────────╯
```

## Project tree

The project follows the tree structure described below :

```text
.
├── app                  # Entrypoints (FastAPI)
├── data                 # Datasets
├── docs                 # Mkdocs
├── models               # Model checkpoints
├── notebooks            # Experiment notebooks or code examples
├── project_name         # Your package
│   ├── data                 # Data loading logic
│   ├── model                # Model logic
│   ├── config.py            # Pydantic config loading
│   ├── main.py
│   └── ...
├── scripts              # Utility shell scripts (build img docker ...)
├── tests                # Unit tests. Should follow the structure of the package.
├── Dockerfile           # Container definition
├── Makefile             # Useful commands
├── mkdocs.yml           # Config for mkdocs
├── pyproject.toml       # UV package and dev tools config
└── README.md
```

## Acknowledgments

This project is based on the Python CookieCutter template created by <PERSON>gemini Invent.
