import argparse
import json
from pathlib import Path
from typing import Any, Callable, Optional, Sequence

import yaml
from pydantic import BaseModel

from cyber_ai_agent.models.schemas import Objective


###############################################################################
# Configuration models (type-safe & validated)                                #
###############################################################################
class LLMCfg(BaseModel):
    model: str
    api_key: str
    temperature: float = 0.0


class TemplateAttackerCfg(BaseModel):
    max_calls: int = 3
    num_variants: int = 50
    quick_trials: int = 3
    success_threshold: float = 1 / 3
    robust_trials: int = 30
    report_top_k: int = 3
    templates: Sequence[str] = []


class LLMGenerateAttackerCfg(BaseModel):
    max_calls: int
    n_iterations: int
    attacker_style: str


class ContextOverloadAttackerCfg(BaseModel):
    max_calls: int = 3
    max_context_length: int = 4000
    n_iterations: int = 10


class SessionCfg(BaseModel):
    app_name: str
    user_id: str
    session_id: str


#####################################################################################


class Settings(BaseModel):
    llm_victim_config: LLMCfg
    llm_attacker_config: LLMCfg
    llm_judge_config: LLMCfg
    template_attacker_config: TemplateAttackerCfg
    llm_generated_attacker_config: LLMGenerateAttackerCfg
    context_overload_attacker_config: ContextOverloadAttackerCfg
    objectives: Sequence[Objective]
    session: SessionCfg


class StreamlitSettings(BaseModel):
    objectives: list[Objective] | None = None
    num_variants: int | None = None
    json_output: bool = False
    victim_model: str | None = None
    victim_api_key: str | None = None
    victim_temperature: float | None = None
    attacker_types: list[str] | None = None
    n_iterations: int | None = None
    attacker_model: str | None = None
    attacker_style: str | None = None


THIS_DIR = Path(__file__).resolve().parent
CONFIG_PATH = THIS_DIR / "config.yaml"
TEMPLATE_ATTACKER_PROMPTS_PATH = (
    THIS_DIR / ".." / "data" / "prompt_injection" / "templates_prompt_injection.json"
)


def load_settings(path: Path = CONFIG_PATH) -> Settings:
    """Load YAML-based settings and validate them via Pydantic."""
    with path.open("r", encoding="utf-8") as fh:
        raw_cfg = yaml.safe_load(fh)
    return Settings.model_validate(raw_cfg)


def load_template_prompts(path: Path = TEMPLATE_ATTACKER_PROMPTS_PATH) -> list[str]:
    """Load template prompts from a JSON file."""
    with path.open("r", encoding="utf-8") as fh:
        data = json.load(fh)

    templates = data["templates"]
    templates = [prompt for prompt in templates if "[INSERT PROMPT HERE]" in prompt]

    return templates


def parse_args() -> StreamlitSettings:
    parser = argparse.ArgumentParser()
    arg_defs: list[tuple[str, Optional[Callable[[str], Any]], str, Any, Optional[str]]] = [
        # (name, type, help, default, action)
        ("--objectives", str, "List of ojbectives in JSON format", None, None),
        ("--json_output", None, "If we want outputs in JSON format", None, "store_true"),
        ("--victim_model", str, "Name of the victim model", None, None),
        ("--victim_api_key", str, "API key of the victim model", None, None),
        ("--victim_temperature", float, "Temperature of the victim model", None, None),
        ("--num_variants", int, "Number of templates for the Template Attacker", None, None),
        (
            "--attacker_types",
            str,
            "Type(s) of Attacker ('template', 'llm_generated', or 'context_overload'), can be specified multiple times",
            None,
            None,
        ),
        ("--n_iterations", int, "Max number of iterations for the LLM attack", None, None),
        ("--attacker_model", str, "Name of the attacker model", None, None),
        ("--attacker_style", str, "Style of the llm attack", None, None),
    ]

    for name, typ, help_text, default, action in arg_defs:
        kwargs: dict[str, Any] = {"help": help_text}

        if name == "--attacker_types":
            kwargs["nargs"] = "+"
        if typ is not None:
            kwargs["type"] = typ
        if default is not None:
            kwargs["default"] = default
        if action is not None:
            kwargs["action"] = action
        parser.add_argument(name, **kwargs)

    args = parser.parse_args()

    # Parse les objectifs JSON s'ils sont fournis
    objectives = None
    if args.objectives:
        objectives_data = json.loads(args.objectives)
        objectives = [Objective.model_validate(obj) for obj in objectives_data]

    # Ensure attacker_types is always a list or None
    attacker_types = args.attacker_types if hasattr(args, "attacker_types") else None
    if attacker_types is not None and not isinstance(attacker_types, list):
        attacker_types = [attacker_types]

    # Return all new args for use in main
    return StreamlitSettings(
        objectives=objectives,
        num_variants=args.num_variants,
        json_output=args.json_output,
        victim_model=args.victim_model,
        victim_api_key=args.victim_api_key,
        victim_temperature=args.victim_temperature,
        attacker_types=attacker_types,
        n_iterations=args.n_iterations,
        attacker_model=args.attacker_model,
        attacker_style=args.attacker_style,
    )
