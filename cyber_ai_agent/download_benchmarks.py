#!/usr/bin/env python3
"""
Download jailbreak and prompt-injection benchmarks from various sources.

This script downloads six public jailbreak/prompt-injection benchmarks and
saves each split to CSV format in the benchmarks directory.

Benchmarks:
1. AdvBench (walledai/AdvBench) - HuggingFace
2. JailbreakBench Behaviors (JailbreakBench/JBB-Behaviors) - HuggingFace
3. WildJailbreak (allenai/wildjailbreak) - HuggingFace
4. JailBreakV-28K (JailbreakV-28K/JailBreakV-28k) - HuggingFace
5. Qualifire Prompt-Injection Benchmark - HuggingFace
6. "In-The-Wild" Jailbreak Prompts - GitHub repository

Requirements:
    pip install datasets pandas requests tqdm
    For gated datasets: run 'huggingface-cli login' or set HF_TOKEN env var
"""

import argparse
import getpass
import os
from pathlib import Path
from typing import Dict, Optional, Tuple, Union

from datasets import DatasetDict, load_dataset
from huggingface_hub import login
from loguru import logger


class BenchmarkDownloader:
    """Handles downloading and processing benchmark datasets."""

    def __init__(self, output_dir: Path) -> None:
        """Initialize the downloader with output directory.

        Args:
            output_dir: Directory to save downloaded benchmark files
        """
        self.output_dir = output_dir
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # Dataset configurations: (hf_id, config_name) or just hf_id
        self.hf_datasets: Dict[str, Union[str, Tuple[str, str]]] = {
            "AdvBench": "walledai/AdvBench",
            "JBB_Behaviors": ("JailbreakBench/JBB-Behaviors", "behaviors"),
            "JailBreakV28K": ("JailbreakV-28K/JailBreakV-28k", "JailBreakV_28K"),
            "Qualifire": ("qualifire/Qualifire-prompt-injection-benchmark"),
        }

    def download_hf_dataset(
        self, dataset_name: str, hf_config: Union[str, Tuple[str, str]]
    ) -> None:
        """Download and save a HuggingFace dataset.

        Args:
            dataset_name: Name for the dataset (used in output filename)
            hf_config: Either a string (dataset ID) or tuple (dataset ID,
                      config name)
        """
        try:
            if isinstance(hf_config, tuple):
                dataset_id, config_name = hf_config
                logger.info(
                    f"▶ Downloading {dataset_name}... ({dataset_id}, config: {config_name})"
                )
                dataset = load_dataset(dataset_id, config_name)
            else:
                dataset_id = hf_config
                logger.info(f"▶ Downloading {dataset_name}... ({dataset_id})")
                dataset = load_dataset(dataset_id)

            self._save_dataset_splits(dataset, dataset_name)

        except Exception as error:
            logger.error(f"❌ Failed to download {dataset_name}: {error}")
            if self._is_authentication_error(error):
                logger.error(
                    "   This dataset requires authentication. Please run "
                    "'huggingface-cli login' or set HF_TOKEN environment "
                    "variable."
                )

    def _save_dataset_splits(self, dataset: DatasetDict, dataset_name: str) -> None:
        """Save all splits of a dataset to CSV files.

        Args:
            dataset: The dataset to save
            dataset_name: Base name for output files
        """
        for split_name, split_data in dataset.items():
            output_path = self.output_dir / f"{dataset_name}_{split_name}.csv"
            split_data.to_pandas().to_csv(output_path, index=False)
            logger.info(f"   ↳ Saved {output_path} ({len(split_data):,} rows)")

    def _is_authentication_error(self, error: Exception) -> bool:
        """Check if an error is related to authentication.

        Args:
            error: The exception to check

        Returns:
            True if the error is authentication-related
        """
        error_message = str(error).lower()
        return "gated dataset" in error_message or "authentication" in error_message

    def download_all_datasets(self) -> None:
        """Download all configured HuggingFace datasets."""
        for dataset_name, hf_config in self.hf_datasets.items():
            self.download_hf_dataset(dataset_name, hf_config)


class AuthenticationManager:
    """Handles HuggingFace authentication."""

    @staticmethod
    def get_auth_token() -> Optional[str]:
        """Get HuggingFace authentication token from various sources.

        Returns:
            Authentication token if found, None otherwise
        """
        # Try environment variable first
        hf_token = os.getenv("HF_TOKEN")
        if hf_token:
            return hf_token

        # Try command line argument
        parser = argparse.ArgumentParser(
            description="Download jailbreak/prompt-injection benchmarks"
        )
        parser.add_argument("--token", "-t", help="HuggingFace auth token for gated datasets")
        args, _ = parser.parse_known_args()

        if args.token:
            return str(args.token)

        # Prompt user if no token found
        return AuthenticationManager._prompt_user_for_token()

    @staticmethod
    def _prompt_user_for_token() -> Optional[str]:
        """Prompt user for HuggingFace token.

        Returns:
            User-provided token if given, None otherwise
        """
        print("\nSome datasets may require authentication.")
        response = input("Do you have a HuggingFace token? (y/n): ").lower().strip()

        if response in ["y", "yes"]:
            token = getpass.getpass("Enter your HuggingFace token: ")
            if token.strip():
                return token

        return None

    @staticmethod
    def authenticate(token: Optional[str]) -> bool:
        """Authenticate with HuggingFace using the provided token.

        Args:
            token: HuggingFace authentication token

        Returns:
            True if authentication successful, False otherwise
        """
        if not token:
            logger.info("No authentication token provided. Some datasets may fail.")
            return False

        try:
            login(token)
            logger.info("✓ Authenticated with HuggingFace")
            return True
        except Exception as error:
            logger.warning(f"Failed to authenticate with token: {error}")
            return False


def main() -> None:
    """Main function to orchestrate the benchmark download process."""
    # Setup paths
    script_dir = Path(__file__).resolve().parent
    output_dir = script_dir / ".." / "data" / "benchmarks"

    # Get authentication token and authenticate
    auth_manager = AuthenticationManager()
    hf_token = auth_manager.get_auth_token()
    auth_manager.authenticate(hf_token)

    # Download all datasets
    downloader = BenchmarkDownloader(output_dir)
    downloader.download_all_datasets()

    logger.info(f"✓ Download complete — files saved to {output_dir}")


if __name__ == "__main__":
    main()
