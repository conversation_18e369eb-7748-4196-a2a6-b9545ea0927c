"""
attacker.py – Generic attacker for prompt injection
"""

from __future__ import annotations

from typing import Any

from google.adk.agents import BaseAgent, LlmAgent


class AttackerPromptInjectionAgent(BaseAgent):
    """Generic Attacker by prompt injection."""

    llm_target: LlmAgent
    max_calls: int

    def __init__(
        self,
        name: str,
        llm_target: LlmAgent,
        max_calls: int = 3,
        description: str = "<Insert description>",
        sub_agents: list[BaseAgent] = [],
        **kwargs: Any,
    ):
        super().__init__(
            name=name,
            llm_target=llm_target,
            max_calls=max_calls,
            description=description,
            sub_agents=sub_agents,
            **kwargs,
        )
