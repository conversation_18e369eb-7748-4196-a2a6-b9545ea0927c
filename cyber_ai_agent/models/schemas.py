"""
models.py – Shared dataclasses used by all attackers/runners.
"""

from __future__ import annotations

from pydantic import BaseModel, Field


class Objective(BaseModel):
    """Represents the high-level goal of an attack run."""

    name: str
    prompt_reference: str


class ObjectivePromptInjection(Objective):
    """Concrete prompt-injection (jailbreak) objective."""

    pass


class LogAttack(BaseModel):
    """Holds telemetry for each LLM call during an attack run."""

    iteration: int
    prompt: str
    response: str
    attacker_kind: str
    objective: Objective
    template: str | None = Field(default=None, description="Template used for the attack, if applicable.")
    score: float | None = Field(default=None, description="Score of the attack, if applicable.")
    cost_total: float | None = Field(default=None, description="Consumptional cost if available.")

class EvalResult(BaseModel):
    evaluation_status: str
    evaluation_response: str | None = Field(default=None, description="Evaluation response if available.")

class EvalInput(BaseModel):
    objective: Objective
    prompt: str
    response: str

class AttackResult(BaseModel):
    """Holds telemetry for each LLM call during an attack run."""

    iteration: int
    prompt: str
    response: str
    attacker_kind: str
    objective: Objective
    template: str | None = Field(default=None, description="Template used for the attack, if applicable.")
    score: float | None = Field(default=None, description="Score of the attack, if applicable.")
    cost_total: float | None = Field(default=None, description="Consumptional cost if available.")
    evaluation: EvalResult | None = Field(default=None, description="Evaluation if available.")
    target_model: str = Field(description="Target model.")
    timestamp: str = Field(description="Time of the attack.")
    success: bool | None = Field(default=None, description="Result from evaluator if available.")
