# app/cyber_ai_agent/config.py
"""
Ce module gère la connexion au client MongoDB.
Il implémente un modèle de singleton pour le client MongoDB
afin d'assurer qu'une seule instance est utilisée à travers l'application.
"""

import os

from pymongo import MongoClient
from pymongo.errors import ConnectionFailure
from pymongo.synchronous.database import Database

# Variables globales pour le client et la base de données MongoDB
_mongo_client = None
_database = None

# --- Configuration de l'authentification MongoDB ---
# Configuration via variables d'environnement pour la sécurité
MONGODB_USERNAME = os.getenv("MONGODB_USERNAME", "cyber_admin")
MONGODB_PASSWORD = os.getenv("MONGODB_PASSWORD", "cyber_secure_password_2024")
MONGODB_HOST = os.getenv("MONGODB_HOST", "localhost")
MONGODB_PORT = os.getenv("MONGODB_PORT", "27017")
MONGODB_APP_DB_NAME = os.getenv("MONGODB_APP_DB_NAME", "cyber_ai_agent_db")


def get_mongo_client() -> MongoClient:
    """
    Retourne une instance du client MongoDB.
    Si le client n'est pas encore initialisé, il tente de se connecter à MongoDB.
    Garantit une seule instance du client (singleton).
    """
    global _mongo_client
    if _mongo_client is None:
        try:
            # Construction de l'URI de connexion avec authentification
            mongo_uri = f"mongodb://{MONGODB_USERNAME}:{MONGODB_PASSWORD}@{MONGODB_HOST}:{MONGODB_PORT}/{MONGODB_APP_DB_NAME}"

            # Pour l'utilisateur root, authSource est 'admin', pour l'utilisateur app, c'est la DB app
            auth_source = "admin" if MONGODB_USERNAME in ["root", "admin"] else MONGODB_APP_DB_NAME
            _mongo_client = MongoClient(
                mongo_uri, serverSelectionTimeoutMS=5000, authSource=auth_source
            )

            # Nous testons toujours la connexion en envoyant une commande 'ping' à la base de données de l'application.
            _mongo_client[MONGODB_APP_DB_NAME].command("ping")
        except ConnectionFailure as e:
            print(f"Erreur de connexion à MongoDB : {e}")
            _mongo_client = None  # Réinitialise le client si la connexion échoue
            raise ConnectionError(
                f"Échec de la connexion à MongoDB : {e}. Assurez-vous que MongoDB est en cours d'exécution et que les identifiants sont corrects."
            )
        except Exception as e:
            print(f"Une erreur inattendue est survenue lors de la connexion à MongoDB : {e}")
            _mongo_client = None
            raise
    return _mongo_client


def get_database(db_name: str = MONGODB_APP_DB_NAME) -> Database:
    """
    Retourne une instance de la base de données MongoDB spécifiée.
    Utilise le client singleton.
    """
    global _database
    # Si la base de données n'est pas encore sélectionnée ou si le client a été réinitialisé
    if _database is None or _database.client != _mongo_client:
        client = get_mongo_client()
        if client:
            _database = client[db_name]
        else:
            raise ConnectionError(
                "Impossible d'obtenir une connexion MongoDB. Le client n'est pas disponible."
            )
    return _database


def close_mongo_client() -> None:
    """
    Ferme la connexion au client MongoDB si elle est ouverte.
    """
    global _mongo_client, _database
    if _mongo_client:
        _mongo_client.close()
        print("Connexion MongoDB fermée.")
        _mongo_client = None
        _database = None
