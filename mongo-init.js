// MongoDB initialization script
// This script runs when the MongoDB container starts for the first time

// Switch to the application database
db = db.getSiblingDB('cyber_ai_agent_db');

// Create a user for the application database
db.createUser({
  user: 'cyber_admin',
  pwd: 'cyber_secure_password_2024',
  roles: [
    {
      role: 'readWrite',
      db: 'cyber_ai_agent_db'
    },
    {
      role: 'dbAdmin',
      db: 'cyber_ai_agent_db'
    }
  ]
});

// Create the attack_results collection with validation
db.createCollection('attack_results', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: [
        'iteration', 'template', 'prompt', 'response', 'attacker_kind', 'objective',
        'score', 'cost_total', 'evaluation', 'target_model', 'timestamp', 'success'
      ],
      properties: {
        iteration: { bsonType: 'int' },
        template: { bsonType: 'string'},
        prompt: { bsonType: 'string' },
        response: { bsonType: 'string' },
        attacker_kind: { bsonType: 'string' },
        objective: {
          bsonType: 'object',
          required: ['name', 'prompt_reference'],
          properties: {
            name: { bsonType: 'string' },
            prompt_reference: { bsonType: 'string' }
          }
        },
        score: { bsonType: ['int', 'double'] },
        cost_total: { bsonType: ['double', 'null'] },
        evaluation: {
          bsonType: 'object',
          required: ['evaluation_status', 'evaluation_response'],
          properties: {
            evaluation_status: {
              bsonType: 'string',
              enum: ['success', 'failure']
            },
            evaluation_response: { bsonType: 'string' }
          }
        },
        target_model: { bsonType: 'string' },
        timestamp: { bsonType: 'date' },
        success: { bsonType: 'bool' }
      }
    }
  }
});

print('Database initialization completed successfully!');
