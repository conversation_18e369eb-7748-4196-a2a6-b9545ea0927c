repos:
- repo: https://github.com/astral-sh/uv-pre-commit
  # uv version.
  rev: 0.6.11
  hooks:
    - id: uv-lock
- repo: https://github.com/pre-commit/pre-commit-hooks
  rev: v5.0.0
  hooks:
    - id: check-added-large-files
    - id: check-ast
    - id: check-byte-order-marker
    - id: check-case-conflict
    - id: check-docstring-first
    - id: check-executables-have-shebangs
      exclude: '\.py$'  # This regex excludes all Python files
    - id: check-json
    - id: check-xml
    - id: check-yaml
      name: Check MkDocs.yml in unsafe mode
      files: ^mkdocs\.yml$
      args: ['--unsafe']
    - id: check-yaml
      name: Check all other YAML files in safe mode
      files: \.ya?ml$
      exclude: ^mkdocs\.yml$
    - id: debug-statements
    - id: end-of-file-fixer
      exclude: ^docs/
    # - id: pretty-format-json
    #   exclude: ^examples/
    #   args: ['--autofix']
    - id: trailing-whitespace
      exclude: ^docs/
        # Make sure windows user don't push CRLF terminated files
        # More doc about it: https://www.aleksandrhovhannisyan.com/blog/crlf-vs-lf-normalizing-line-endings-in-git/
    - id: mixed-line-ending
      args: ['--fix=lf']
      exclude: ^docs/
    - id: detect-private-key
- repo: https://github.com/pre-commit/mirrors-mypy
  rev: v1.16.0
  hooks:
    - id: mypy
      name: mypy
      language: python
      language_version: python3.12
      entry: mypy
      types: [python]
      args: [--config-file=pyproject.toml]
      additional_dependencies: [
        types-requests,
        types-PyYAML,
        pandas-stubs
      ]
      exclude: ^tests/
      require_serial: true
- repo: https://github.com/kynan/nbstripout
  rev: 0.8.1
  hooks:
    - id: nbstripout
- repo: https://github.com/astral-sh/ruff-pre-commit
  # Ruff version.
  rev: v0.11.2
  hooks:
    # Run the linter.
    - id: ruff
      args: [ --fix ]
    # Run the formatter.
    - id: ruff-format
- repo: https://github.com/PyCQA/bandit
  rev: 1.8.3
  hooks:
    - id: bandit
      # Exclude tests bc they contain assert statements, that raise bandit errors
      args: [--skip, "B101", --exclude, "tests/,app.py"]
