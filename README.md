# cyber_ai_agent

Development of AI agents for detecting cybersecurity issues on deployed agents.

## Install

Create a virtual environment:

```bash
uv venv .venv --python 3.12  # or any version of python >=3.10
source .venv/bin/activate
uv sync
```

`cyber_ai_agent` is now installed as a package and as a command line tool. If you also want the optional dev and docs dependencies:

```bash
uv sync --extra dev --extra docs
```

## Run the project

The way to run the streamlit interface is
```console
streamlit run app.py
uv run cyber-ai-agent --help
```

The recommended way to run the code of this project is via CLI.:

```console
uv run cyber-ai-agent --help
```

which should return the list of the available commands:

```text
 Usage: cyber-ai-agent [OPTIONS] COMMAND [ARGS]...

 Run all datascience commands.

╭─ Commands ──────────────────────────────────────────────────╮
│ data     Manages data flow.                                 │
│ model    Manages model training and predictions.            │
╰─────────────────────────────────────────────────────────────╯
```

## Documentation

To preview your documentation in real-time while editing, run:

```console
uv sync --extra docs
mkdocs serve
```

More details about the documentation: TODO add link.

## Project tree

The project follows the tree structure described bellow :

```text
.
├── app                  # Entrypoints (fastapi)
├── data                 # Datasets
├── docs                 # Mkdocs
├── models               # Model checkpoints
├── notebooks            # Experiment notebooks or code examples
├── project_name         # Your package
│   ├── data                 # Data loading logic
│   ├── model                # Model logic
│   ├── config.py            # Pydantic config loading
│   ├── main.py
│   └── ...
├── scripts              # Utility shell scripts (build img docker ...)
├── tests                # Unit tests. Should follow the structure of the package.
├── Dockerfile           # Container definition
├── Makefile             # Useful commands
├── mkdocs.yml           # Config for mkdocs
├── pyproject.toml       # UV package and dev tools config
└── README.md
```

## Configuration Management

To change configuration sources between dev and prod modes, set the environment variable `ENV=dev` or `ENV=prod`.

## Acknowledgments

This project is based on the Python CookieCutter template created by Capgemini Invent.
