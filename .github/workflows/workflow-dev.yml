name: Type Checking

on:
  push:
    branches:
      - '**'  # This will run on all branches
  pull_request:
    branches:
      - '**'

jobs:
  check-code-quality:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Set up Python 3.12
      uses: actions/setup-python@v4
      with:
        python-version: 3.12

    - name: Install uv
      run: |
        curl -LsSf https://astral.sh/uv/install.sh | sh
    - name: Install dependencies
      run: |
        uv sync --extra dev
    - name: Run ruff
      run: |
        uv run ruff check cyber_ai_agent mongodb app tests
    - name: Run mypy
      run: |
        uv run mypy cyber_ai_agent mongodb app
    - name: Run pytest
      run: |
        uv run pytest tests/
