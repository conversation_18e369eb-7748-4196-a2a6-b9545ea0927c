services:
  mongodb:
    image: mongo:latest # Utilise la dernière image officielle de MongoDB
    container_name: cyber-ai-mongodb # Nom du conteneur pour faciliter l'identification
    ports:
      - "27017:27017" # Mappe le port 27017 du conteneur au port 27017 de l'hôte
    environment:
      # Variables d'environnement pour créer un utilisateur root lors du premier démarrage
      # Ces variables sont lues par le script d'entrée de l'image Docker de MongoDB
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_ROOT_USERNAME}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD}
      MONGO_INITDB_DATABASE: ${MONGODB_APP_DB_NAME}



    volumes:
      # Montage d'un volume persistant pour stocker les données de MongoDB.
      # Cela garantit que vos données ne sont pas perdues lorsque le conteneur est arrêté/supprimé.
      - mongodb_data:/data/db

      # Script d'initialisation pour créer la base de données et les collections
      - ./mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro

    restart: always # Redémarre le conteneur si Docker est redémarré ou en cas de crash
    healthcheck: # Vérifie la santé du service MongoDB
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  coordinator:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: coordinator_agent
    depends_on:
      - mongodb
    environment:
      # MongoDB configuration for the application
      MONGODB_HOST: mongodb
      MONGODB_PORT: 27017
      MONGODB_USERNAME: ${MONGO_ROOT_USERNAME}
      MONGODB_PASSWORD: ${MONGO_ROOT_PASSWORD}
      MONGODB_APP_DB_NAME: ${MONGODB_APP_DB_NAME}
      MONGO_URI: "mongodb://${MONGO_ROOT_USERNAME}:${MONGO_ROOT_PASSWORD}@mongodb:27017/${MONGODB_APP_DB_NAME}"
      GOOGLE_API_KEY: ${GOOGLE_API_KEY}
    # No ports mapping for coordinator unless it's a web app you want to expose

  # 🖥️ Interface Web MongoDB - Mongo Express
  mongo-express:
    image: mongo-express:latest
    container_name: mongo-express-ui
    restart: unless-stopped
    depends_on:
      - mongodb
    ports:
      - "8081:8081"  # Interface accessible sur http://localhost:8081
    environment:
      # Configuration de connexion à MongoDB
      ME_CONFIG_MONGODB_ADMINUSERNAME: ${MONGO_ROOT_USERNAME}
      ME_CONFIG_MONGODB_ADMINPASSWORD: ${MONGO_ROOT_PASSWORD}
      ME_CONFIG_MONGODB_URL: "mongodb://${MONGO_ROOT_USERNAME}:${MONGO_ROOT_PASSWORD}@mongodb:27017/"
      ME_CONFIG_MONGODB_SERVER: mongodb
      ME_CONFIG_MONGODB_PORT: 27017

      # Configuration de l'interface
      ME_CONFIG_BASICAUTH: false  # Pas d'authentification supplémentaire
      ME_CONFIG_MONGODB_ENABLE_ADMIN: true
      ME_CONFIG_SITE_BASEURL: /

      # Personnalisation
      ME_CONFIG_SITE_COOKIESECRET: cyber_ai_secret_2024
      ME_CONFIG_SITE_SESSIONSECRET: cyber_ai_session_2024

volumes:
  mongodb_data:
