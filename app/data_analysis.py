import os

import pandas as pd
import streamlit as st
from pymongo.cursor import <PERSON>ursor
from pymongo.synchronous.collection import Collection
from pymongo.synchronous.database import Database
from result_page import render_card

from cyber_ai_agent.mongo_utils import close_mongo_client, get_database

MONGODB_APP_DB_NAME = os.getenv("MONGODB_APP_DB_NAME", "cyber_ai_agent_db")


def show_data_from_db() -> None:
    # Get database instance
    db: Database = get_database(MONGODB_APP_DB_NAME)
    collection_name = "attack_results"

    # Get collection
    collection: Collection = db[collection_name]

    # Fetch all documents
    cursor: Cursor = collection.find()
    docs: list = list(cursor)
    if not docs:
        st.info("No data found in 'attack_results' collection.")
        return

    # Convert to DataFrame
    df = pd.DataFrame(docs)

    # Filter by attacker_type if column exists
    if "attacker_kind" in df.columns:
        attacker_types = ["All"] + sorted(df["attacker_kind"].dropna().unique().tolist())
        selected_type = st.selectbox("Filter by attacker_kind", attacker_types)
        if selected_type != "All":
            df = df[df["attacker_kind"] == selected_type].copy()

    if "success" in df.columns:
        mask_success = df["success"].fillna(False).astype(bool)
        df_success = df[mask_success].copy()
    else:
        df_success = pd.DataFrame()

    st.markdown(
        f"""
        <div style='display: flex; gap: 2em; margin-bottom: 1em;'>
            {render_card("Number of attacks logged in DB", str(len(df)), "2.2em")}
            {render_card("Number of successful attacks", str(len(df_success)), "2.2em", "#27ae60")}
            {render_card("Success rate (%)", f"{(len(df_success) / len(df) * 100):.1f}%" if len(df) > 0 else "N/A", "2.2em", "#800080")}
        </div>
        """,
        unsafe_allow_html=True,
    )

    st.markdown(f"## {collection_name}")
    if "_id" in df.columns:
        df["_id"] = df["_id"].astype(str)
    # Show only relevant columns if present
    # columns_to_show = [col for col in ["_id", "prompte", "heure", "reponse"] if col in df.columns]
    st.dataframe(df, hide_index=True)
    close_mongo_client()
