# result_page.py

import textwrap

import pandas as pd
import streamlit as st

from cyber_ai_agent.models.schemas import AttackResult
from cyber_ai_agent.runner import Settings


def render_card(
    title: str,
    value: str,
    value_font_size: str = "1.5em",
    value_color: str = "#34495e",
    background_color: str="#f4f6fa",
) -> str:
    return textwrap.dedent(f"""
        <div style='background: {background_color}; border-radius: 16px; padding: 1.2em 2em; min-width: 180px; box-shadow: 0 2px 8px #0001;'>
            <div style='font-size: 1.1em; color: #555;'>{title}</div>
            <div style='font-size: {value_font_size}; font-weight: bold; color: {value_color};'>{value}</div>
        </div>
    """).strip()


def show_attack_results(attack_response: dict | None) -> None:
    if attack_response:
        attack_response_object = attack_response  # If you need to validate, do it here
        settings = Settings.model_validate(attack_response_object["settings"])

        attack_logs_dicts = attack_response_object["attack_logs"]
        attack_logs = [AttackResult.model_validate(log) for log in attack_logs_dicts]
        if attack_logs:
            data = []
            for log in attack_logs:
                data.append(
                    {
                        "Iteration": log.iteration,
                        "Objective": log.objective.name,
                        "Prompt": log.objective.prompt_reference,
                        "Template": log.template,
                        "Full Prompt": log.prompt,
                        "Response": log.response,
                        "Attack Type": log.attacker_kind,
                        "Score": log.score,
                        "Success": log.success,
                    }
                )
            df = pd.DataFrame(data)

            attacker_types = sorted(df["Attack Type"].dropna().unique().tolist())

            ##############################################################################
            st.markdown("### Attack Summary")
            summary_cards = []
            attacker_type_labels = {
                "template": "Template Attacker",
                "llm_generated": "LLM Attacker",
            }
            for attacker_type in attacker_types:
                is_template_attack_successful: bool = (
                    (df["Attack Type"] == attacker_type) & (df["Success"])
                ).any()
                label = attacker_type_labels.get(attacker_type, attacker_type)
                summary_cards.append(
                    render_card(
                        f"Vulnerability to {label}",
                        "Yes" if is_template_attack_successful else "No",
                        "2.2em",
                        value_color="#e74c3c" if is_template_attack_successful else "#27ae60",
                        background_color="#faeaea" if is_template_attack_successful else "#eafaf1",
                    )
                )

            st.markdown(
                f"""
                    <div style='display: flex; gap: 2em; margin-bottom: 1em;'>
                         {"".join(summary_cards)}
                    </div>
                    """,
                unsafe_allow_html=True,
            )

            ##############################################################################
            st.markdown("### Attack Information")
            num_llm_generated_attacks: int = (df["Attack Type"] == "llm_generated").sum()
            cards = []
            if "template" in attacker_types:
                cards.append(
                    render_card(
                        "Templates tested",
                        f"{settings.template_attacker_config.num_variants}",
                        "2.2em",
                    )
                )
            if "llm_generated" in attacker_types:
                cards.append(
                    render_card("LLM Attacker iterations", f"{num_llm_generated_attacks}", "2.2em")
                )
            st.markdown(
                f"""
                    <div style='display: flex; gap: 2em; margin-bottom: 1em;'>
                         {"".join(cards)}
                    </div>
                    """,
                unsafe_allow_html=True,
            )

            ##############################################################################
            st.markdown("### Attack Statistics")

            selected_type = st.selectbox("Filter by attacker_kind", ["All"] + attacker_types)
            if selected_type != "All":
                df = df[df["Attack Type"] == selected_type].copy()

            if "Success" in df.columns:
                mask_success = df["Success"].fillna(False).astype(bool)
                df_success = df[mask_success].copy()
            else:
                df_success = pd.DataFrame()

            st.markdown(
                f"""
                <div style='display: flex; gap: 2em; margin-bottom: 1em;'>
                    {render_card("Number of attacks logged in DB", str(len(df)), "2.2em")}
                    {render_card("Number of successful attacks", str(len(df_success)), "2.2em", "#e74c3c" if len(df_success) > 0 else "#34495e")}
                    {render_card("Success rate (%)", f"{(len(df_success) / len(df) * 100):.1f}%" if len(df) > 0 else "N/A", "2.2em", "#800080")}
                </div>
                """,
                unsafe_allow_html=True,
            )
            st.dataframe(df, hide_index=True)
            if selected_type == "template":
                df_grouped = (
                    df.groupby(["Prompt", "Template"])
                    .agg(
                        N_iterations=("Iteration", "count"),
                        Mean_score=("Score", "mean"),
                    )
                    .reset_index()
                    .sort_values(by="Mean_score", ascending=False)
                    .assign(Mean_score=lambda df: df["Mean_score"].round(2))
                )
                st.write("**Summary of attacks by prompt and template:**")
                st.dataframe(df_grouped, hide_index=True)
        else:
            st.warning("No attack logs available.")
    else:
        st.info("No attack results available. Launch an attack in the previous section.")
