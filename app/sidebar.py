import streamlit as st


def render_sidebar() -> None:

    st.markdown("---")

    if st.button("Attack Configuration", key="nav_attack"):
        st.session_state.selected_page = "Attack Configuration"
    if st.button("Results", key="nav_results"):
        st.session_state.selected_page = "Results"
    if st.button("Data Analysis", key="nav_data_analysis"):
        st.session_state.selected_page = "Data Analysis"

    st.markdown("---")
    # st.markdown("<br><br>", unsafe_allow_html=True)

    st.markdown(
        """
        <style>
        section[data-testid="stSidebar"] {
            background: #f4f6fa;
            border-right: 2px solid #27ae60;
            min-width: 260px;
            max-width: 320px;
            font-family: 'Arial', sans-serif;
            padding: 10px;
            z-index: 0;
        }
        section[data-testid="stSidebar"] .stButton > button {
            background-color: #f4f6fa !important;
            color: #2c3e50 !important;
            border: none !important;
            border-radius: 8px !important;
            font-size: 1.5em !important;
            font-weight: normal !important;
            margin-bottom: 4px !important;
            width: 100% !important;
            text-align: left !important;
            transition: background 0.2s;
            box-shadow: none !important;
        }
        section[data-testid="stSidebar"] .stButton > button:hover {
            background-color: #e0e6ef !important;
            color: #2c3e50 !important;
        }
        </style>
        """,
        unsafe_allow_html=True,
    )
