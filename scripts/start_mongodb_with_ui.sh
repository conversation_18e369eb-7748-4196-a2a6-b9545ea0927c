#!/bin/bash

# 🚀 MongoDB + Interface Web Startup Script
# Démarre MongoDB avec l'interface web Mongo Express

set -e  # Exit on any error

echo "🚀 Démarrage de MongoDB avec Interface Web..."
echo "=================================================="

# Vérifier que Docker fonctionne
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker n'est pas démarré. Veuillez démarrer Docker d'abord."
    exit 1
fi

# Function to run docker compose with fallback to docker-compose
run_docker_compose() {
    local cmd="$1"
    shift

    # Try docker compose (V2) first
    if command -v docker >/dev/null 2>&1 && docker compose version >/dev/null 2>&1; then
        echo "🐳 Using docker compose (V2)..."
        docker compose "$cmd" "$@"
    # Fallback to docker-compose (V1)
    elif command -v docker-compose >/dev/null 2>&1; then
        echo "🐳 Using docker-compose (V1)..."
        docker-compose "$cmd" "$@"
    else
        echo "❌ Neither 'docker compose' nor 'docker-compose' is available"
        echo "Please install Docker Compose"
        exit 1
    fi
}

# Vérifier les variables d'environnement
if [ ! -f .env ]; then
    echo "❌ Fichier .env manquant. Création d'un fichier .env par défaut..."
    cat > .env << EOF
# MongoDB Configuration
MONGO_ROOT_USERNAME=cyber_admin
MONGO_ROOT_PASSWORD=cyber_secure_password_2024
MONGODB_APP_DB_NAME=cyber_ai_agent_db
MONGODB_HOST=localhost
MONGODB_PORT=27017

# Google API (optionnel)
GOOGLE_API_KEY=your_google_api_key_here
EOF
    echo "✅ Fichier .env créé avec les valeurs par défaut"
fi

# Charger les variables d'environnement
source .env

echo "📦 Démarrage des conteneurs MongoDB + Interface Web..."
run_docker_compose up -d mongodb mongo-express

echo "⏳ Attente du démarrage de MongoDB..."
sleep 15

# Vérifier que MongoDB est prêt
echo "🔍 Vérification de la santé de MongoDB..."
for i in {1..30}; do
    if run_docker_compose exec -T mongodb mongosh --eval "db.adminCommand('ping')" > /dev/null 2>&1; then
        echo "✅ MongoDB est prêt!"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "❌ MongoDB n'a pas pu démarrer correctement"
        run_docker_compose logs mongodb
        exit 1
    fi
    echo "   Tentative $i/30 - attente..."
    sleep 2
done

# Vérifier que Mongo Express est prêt
echo "🔍 Vérification de Mongo Express..."
sleep 5
for i in {1..20}; do
    if curl -s http://localhost:8081 > /dev/null 2>&1; then
        echo "✅ Mongo Express est prêt!"
        break
    fi
    if [ $i -eq 20 ]; then
        echo "⚠️  Mongo Express pourrait ne pas être complètement prêt, mais continuons..."
        break
    fi
    echo "   Tentative $i/20 - attente de l'interface web..."
    sleep 2
done

# Configuration de la base de données
echo "🔧 Configuration de la base de données..."
if python -m mongodb.create_collections 2>/dev/null; then
    echo "✅ Collections créées avec succès!"
else
    echo "⚠️  Erreur lors de la création des collections (peut être normal si elles existent déjà)"
fi

echo ""
echo "🎉 SUCCÈS! MongoDB + Interface Web sont prêts!"
echo "=================================================="
echo ""
echo "📊 INFORMATIONS DE CONNEXION:"
echo "   🗄️  MongoDB:"
echo "      - Host: localhost:27017"
echo "      - Username: $MONGO_ROOT_USERNAME"
echo "      - Database: $MONGODB_APP_DB_NAME"
echo ""
echo "   🖥️  Interface Web (Mongo Express):"
echo "      - URL: http://localhost:8081"
echo "      - Pas d'authentification requise"
echo "      - Navigation: Bases de données → $MONGODB_APP_DB_NAME → Collections"
echo ""
echo "🔗 ACTIONS RAPIDES:"
echo "   📱 Ouvrir l'interface web:"
if command -v open &> /dev/null; then
    echo "      open http://localhost:8081"
elif command -v xdg-open &> /dev/null; then
    echo "      xdg-open http://localhost:8081"
else
    echo "      Ouvrez http://localhost:8081 dans votre navigateur"
fi
echo ""
echo "   🧪 Tester la connexion:"
echo "      python tests/test_db_write.py"
echo ""
echo "   📋 Voir les logs:"
echo "      docker-compose logs mongodb"
echo "      docker-compose logs mongo-express"
echo ""
echo "   🛑 Arrêter les services:"
echo "      docker-compose down"
echo ""

# Optionnel: Ouvrir automatiquement le navigateur
if command -v open &> /dev/null && [[ "$OSTYPE" == "darwin"* ]]; then
    echo "🌐 Ouverture automatique de l'interface web..."
    sleep 2
    open http://localhost:8081
elif command -v xdg-open &> /dev/null; then
    echo "🌐 Ouverture automatique de l'interface web..."
    sleep 2
    xdg-open http://localhost:8081
fi

echo "✨ Prêt à utiliser! Consultez http://localhost:8081 pour voir vos données MongoDB."
