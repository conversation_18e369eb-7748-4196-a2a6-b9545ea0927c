#!/usr/bin/env python3
"""
MongoDB Setup Script for Cyber AI Agent

This script helps initialize and test the MongoDB connection.
Run this after starting your Docker containers.
"""

import os
import sys
from datetime import datetime

# Add the project root to the path
sys.path.append(os.path.join(os.path.dirname(__file__), ".."))

try:
    from cyber_ai_agent.config import (
        close_mongo_client,
        get_database,
        get_mongo_client,
    )
    from mongodb.create_collections import (
        create_prompt_responses_collection_with_validation,
    )
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running this from the project root directory")
    sys.exit(1)


def test_connection() -> bool:
    """Test the MongoDB connection"""
    print("🔍 Testing MongoDB connection...")
    try:
        client = get_mongo_client()
        # Test the connection
        client.admin.command("ping")
        print("✅ MongoDB connection successful!")
        return True
    except Exception as e:
        print(f"❌ MongoDB connection failed: {e}")
        return False


def setup_collections() -> bool:
    """Create collections with validation"""
    print("📦 Setting up MongoDB collections...")
    try:
        create_prompt_responses_collection_with_validation()
        print("✅ Collections setup completed!")
        return True
    except Exception as e:
        print(f"❌ Collection setup failed: {e}")
        return False


def test_write_operation() -> bool:
    """Test writing to the database"""
    print("✍️  Testing write operations...")
    try:
        db = get_database()
        collection = db["prompt_responses"]

        # Test document
        test_doc = {
            "prompte": "Test prompt from setup script",
            "heure": datetime.now(),
            "reponse": "Test response - MongoDB is working correctly!",
        }

        # Insert the document
        result = collection.insert_one(test_doc)
        print(f"✅ Test document inserted with ID: {result.inserted_id}")

        # Verify the insertion
        found_doc = collection.find_one({"_id": result.inserted_id})
        if found_doc:
            print("✅ Document verification successful!")
            # Clean up test document
            collection.delete_one({"_id": result.inserted_id})
            print("🧹 Test document cleaned up")
            return True
        else:
            print("❌ Document verification failed!")
            return False

    except Exception as e:
        print(f"❌ Write operation test failed: {e}")
        return False


def main() -> bool:
    """Main setup function"""
    print("🚀 Starting MongoDB setup for Cyber AI Agent...")
    print("=" * 50)

    # Test connection
    if not test_connection():
        print("\n💡 Make sure your Docker containers are running:")
        print("   docker-compose up -d")
        return False

    # Setup collections
    if not setup_collections():
        return False

    # Test write operations
    if not test_write_operation():
        return False

    print("\n" + "=" * 50)
    print("🎉 MongoDB setup completed successfully!")
    print("\n📋 Next steps:")
    print("   1. Your MongoDB is ready to use")
    print("   2. Collections are created with proper validation")
    print("   3. You can now run your application")

    # Close connection
    close_mongo_client()
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
