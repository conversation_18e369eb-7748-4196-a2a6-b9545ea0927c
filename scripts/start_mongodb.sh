#!/bin/bash

# MongoDB Startup Script for Cyber AI Agent
# This script starts MongoDB and sets up the database

set -e  # Exit on any error

echo "🚀 Starting MongoDB for Cyber AI Agent..."
echo "=" * 50

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Function to run docker compose with fallback to docker-compose
run_docker_compose() {
    local cmd="$1"
    shift

    # Try docker compose (V2) first
    if command -v docker >/dev/null 2>&1 && docker compose version >/dev/null 2>&1; then
        echo "🐳 Using docker compose (V2)..."
        docker compose "$cmd" "$@"
    # Fallback to docker-compose (V1)
    elif command -v docker-compose >/dev/null 2>&1; then
        echo "🐳 Using docker-compose (V1)..."
        docker-compose "$cmd" "$@"
    else
        echo "❌ Neither 'docker compose' nor 'docker-compose' is available"
        echo "Please install Docker Compose"
        exit 1
    fi
}

# Start MongoDB service
echo "📦 Starting MongoDB container..."
run_docker_compose up -d mongodb

# Wait for MongoDB to be ready
echo "⏳ Waiting for MongoDB to be ready..."
sleep 10

# Check if MongoDB is healthy
echo "🔍 Checking MongoDB health..."
for i in {1..30}; do
    if run_docker_compose exec -T mongodb mongosh --eval "db.adminCommand('ping')" > /dev/null 2>&1; then
        echo "✅ MongoDB is ready!"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "❌ MongoDB failed to start properly"
        run_docker_compose logs mongodb
        exit 1
    fi
    echo "   Attempt $i/30 - waiting..."
    sleep 2
done

# Run the setup script
echo "🔧 Setting up database and collections..."
if python scripts/setup_mongodb.py; then
    echo "✅ MongoDB setup completed successfully!"
    echo ""
    echo "📋 MongoDB is now ready to use:"
    echo "   - Database: cyber_ai_agent_db"
    echo "   - Host: localhost:27017"
    echo "   - Username: cyber_admin"
    echo ""
    echo "🔗 Useful commands:"
    echo "   make mongodb-stop    # Stop MongoDB"
    echo "   make start-all       # Start all services"
    echo "   docker compose logs mongodb  # View logs (or docker-compose)"
else
    echo "❌ MongoDB setup failed"
    exit 1
fi
